package cn.microhis.bcs.web;

import cn.com.idmy.auth.interceptor.AuthInterceptor;
import cn.com.idmy.auth.router.RouterMatcher;
import cn.com.idmy.base.util.AppUtil;
import cn.com.idmy.cloud.config.FeignConfig;
import cn.microhis.bcs.check.config.BcsCheckConfig;
import cn.microhis.bcs.config.BcsCoreConfig;
import cn.microhis.bcs.deposit.config.BcsDepositConfig;
import cn.microhis.cloud.auth.Currents;
import cn.microhis.invoice.web.InvoiceWebApp;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Slf4j
@EnableScheduling
@SpringBootApplication
@EnableConfigurationProperties
@EnableAsync(proxyTargetClass = true)
@Import({
        BcsCoreConfig.class,
        BcsDepositConfig.class,
        BcsCheckConfig.class,
        FeignConfig.class,
        InvoiceWebApp.class
})
@EnableFeignClients(basePackages = "cn.microhis.bcs.feign")
public class BcsWebApp implements WebMvcConfigurer, SmartInitializingSingleton {
    static boolean isMain = false;

    static {
        AppUtil.register("bcs");
    }

    public static void main(String[] args) {
        isMain = true;
        SpringApplication.run(BcsWebApp.class, args);
    }

    @Override
    public void addInterceptors(@NotNull InterceptorRegistry registry) {
        if (!isMain) return;

        registry.addInterceptor(new AuthInterceptor(handler -> {
            RouterMatcher.match("/**", router -> {
                Currents.check();
            });
        })).addPathPatterns("/**");
    }

    @Override
    public void afterSingletonsInstantiated() {
    }
}