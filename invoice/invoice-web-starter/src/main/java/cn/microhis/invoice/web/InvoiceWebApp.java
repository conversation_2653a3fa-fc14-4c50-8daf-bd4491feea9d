package cn.microhis.invoice.web;

import cn.com.idmy.auth.interceptor.AuthInterceptor;
import cn.com.idmy.auth.router.RouterMatcher;
import cn.com.idmy.base.util.AppUtil;
import cn.com.idmy.cloud.config.FeignConfig;
import cn.microhis.cloud.auth.Currents;
import cn.microhis.invoice.config.InvoiceConfig;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Slf4j
@EnableScheduling
@SpringBootApplication
@EnableConfigurationProperties
@EnableAsync(proxyTargetClass = true)
@Import({InvoiceConfig.class, FeignConfig.class})
public class InvoiceWebApp implements WebMvcConfigurer, SmartInitializingSingleton {
    static boolean isMain = false;

    static {
        AppUtil.register("invoice");
    }


    public static void main(String[] args) {
        isMain = true;
        SpringApplication.run(InvoiceWebApp.class, args);
    }

    @Override
    public void addInterceptors(@NotNull InterceptorRegistry registry) {
        if (!isMain) return;

        registry.addInterceptor(new AuthInterceptor(handler -> RouterMatcher.match("/**", router -> Currents.check()))).addPathPatterns("/**");
    }

    @Override
    public void afterSingletonsInstantiated() {
    }
}