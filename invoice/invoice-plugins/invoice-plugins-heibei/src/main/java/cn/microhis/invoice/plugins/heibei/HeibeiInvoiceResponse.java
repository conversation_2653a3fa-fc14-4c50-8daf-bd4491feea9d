package cn.microhis.invoice.plugins.heibei;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 河北发票创建响应结果
 */
@Data
public class HeibeiInvoiceResponse {
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 发票代码
     */
    private String ticketCode;
    
    /**
     * 发票号码
     */
    private String ticketNo;
    
    /**
     * 校验码
     */
    private String checkCode;
    
    /**
     * 发票查看URL
     */
    private String invoiceUrl;
    
    /**
     * 发票创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 发票PDF下载地址
     */
    private String pdfUrl;
    
    /**
     * 发票二维码
     */
    private String qrCode;
}
