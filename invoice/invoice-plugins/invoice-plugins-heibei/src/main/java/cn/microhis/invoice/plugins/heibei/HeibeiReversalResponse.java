package cn.microhis.invoice.plugins.heibei;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 河北发票冲红响应结果
 */
@Data
public class HeibeiReversalResponse {
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 冲红发票代码
     */
    private String reversalTicketCode;
    
    /**
     * 冲红发票号码
     */
    private String reversalTicketNo;
    
    /**
     * 校验码
     */
    private String checkCode;
    
    /**
     * 冲红发票查看URL
     */
    private String reversalUrl;
    
    /**
     * 冲红时间
     */
    private LocalDateTime reversalTime;
    
    /**
     * 冲红发票PDF下载地址
     */
    private String pdfUrl;
}
