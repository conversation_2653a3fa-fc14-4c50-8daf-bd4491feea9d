package cn.microhis.invoice.plugins.heibei;

import cn.microhis.invoice.provider.exception.InvoiceHttpException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.text.StrUtil;
import org.dromara.hutool.http.HttpRequest;
import org.dromara.hutool.http.HttpResponse;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 河北发票HTTP工具类
 * 负责与河北发票服务进行HTTP通信
 */
@Slf4j
public class Http {
    
    private static final String CHARSET = "UTF-8";
    private static final int TIMEOUT = 30000; // 30秒超时
    
    /**
     * 发送POST请求到河北发票服务
     */
    public static <T> T post(HeibeiConfig config, Object requestData, String path, Class<T> responseClass) {
        String url = config.getUrl() + path;
        String requestBody = JSON.toJSONString(requestData);
        
        log.info("发送河北发票请求，URL: {}, 请求体: {}", url, requestBody);
        
        try {
            // 构建请求头
            Map<String, String> headers = buildHeaders(config, requestBody);
            
            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(url)
                    .headerMap(headers, true)
                    .body(requestBody)
                    .timeout(TIMEOUT)
                    .execute();
            
            String responseBody = response.body();
            log.info("河北发票响应，状态码: {}, 响应体: {}", response.getStatus(), responseBody);
            
            if (!response.isOk()) {
                throw new InvoiceHttpException("HTTP请求失败，状态码: " + response.getStatus() + ", 响应: " + responseBody);
            }
            
            // 解析响应
            if (StrUtil.isBlank(responseBody)) {
                throw new InvoiceHttpException("响应体为空");
            }
            
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            
            // 检查业务状态
            checkBusinessStatus(jsonResponse);
            
            // 转换为目标类型
            return jsonResponse.toJavaObject(responseClass);
            
        } catch (Exception e) {
            log.error("河北发票HTTP请求异常，URL: {}", url, e);
            if (e instanceof InvoiceHttpException) {
                throw e;
            }
            throw new InvoiceHttpException("HTTP请求异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建请求头
     */
    private static Map<String, String> buildHeaders(HeibeiConfig config, String requestBody) {
        Map<String, String> headers = new HashMap<>();
        
        // 基本头信息
        headers.put("Content-Type", "application/json;charset=" + CHARSET);
        headers.put("Accept", "application/json");
        headers.put("User-Agent", "HeibeiInvoicePlugin/1.0");
        
        // 时间戳
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        headers.put("X-Timestamp", timestamp);
        
        // 应用ID
        headers.put("X-App-Id", config.getAppId());
        
        // 机构代码
        headers.put("X-Org-Code", config.getOrgCode());
        
        // 签名
        String signature = generateSignature(config, requestBody, timestamp);
        headers.put("X-Signature", signature);
        
        return headers;
    }
    
    /**
     * 生成签名
     */
    private static String generateSignature(HeibeiConfig config, String requestBody, String timestamp) {
        try {
            // 构建签名参数
            Map<String, String> signParams = new TreeMap<>();
            signParams.put("appId", config.getAppId());
            signParams.put("orgCode", config.getOrgCode());
            signParams.put("timestamp", timestamp);
            signParams.put("body", requestBody);
            
            // 构建签名字符串
            StringBuilder signStr = new StringBuilder();
            for (Map.Entry<String, String> entry : signParams.entrySet()) {
                if (signStr.length() > 0) {
                    signStr.append("&");
                }
                signStr.append(entry.getKey()).append("=").append(entry.getValue());
            }
            signStr.append("&key=").append(config.getAppSecret());
            
            // MD5签名
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(signStr.toString().getBytes(StandardCharsets.UTF_8));
            
            // 转换为十六进制字符串
            StringBuilder hexStr = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexStr.append('0');
                }
                hexStr.append(hex);
            }
            
            return hexStr.toString().toUpperCase();
            
        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new InvoiceHttpException("生成签名失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查业务状态
     */
    private static void checkBusinessStatus(JSONObject response) {
        String code = response.getString("code");
        String message = response.getString("message");
        
        if (!"0000".equals(code) && !"SUCCESS".equals(code)) {
            String errorMsg = StrUtil.isNotBlank(message) ? message : "未知错误";
            throw new InvoiceHttpException("业务处理失败，错误代码: " + code + ", 错误信息: " + errorMsg);
        }
    }
    
    /**
     * HMAC-SHA256签名（备用签名方式）
     */
    private static String hmacSha256(String data, String key) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] digest = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(digest);
        } catch (Exception e) {
            throw new InvoiceHttpException("HMAC-SHA256签名失败: " + e.getMessage(), e);
        }
    }
}
