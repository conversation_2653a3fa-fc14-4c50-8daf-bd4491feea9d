package cn.microhis.invoice.plugins.heibei;

import lombok.Data;

/**
 * 河北发票供应商配置类
 * 包含河北发票服务所需的基本配置信息
 */
@Data
public class HeibeiConfig {
    /**
     * 服务器URL地址
     */
    String url;
    
    /**
     * 应用ID
     */
    String appId;
    
    /**
     * 应用密钥
     */
    String appSecret;
    
    /**
     * 机构代码
     */
    String orgCode;
    
    /**
     * 机构名称
     */
    String orgName;
    
    /**
     * 销售方纳税人识别号
     */
    String sellerTaxNo;
    
    /**
     * 销售方名称
     */
    String sellerName;
    
    /**
     * 销售方电话
     */
    String sellerPhone;
    
    /**
     * 销售方地址
     */
    String sellerAddress;
    
    /**
     * 销售方开户行
     */
    String sellerBank;
    
    /**
     * 销售方银行账号
     */
    String sellerBankAccount;
    
    /**
     * 是否为测试环境
     */
    boolean testMode = false;
}
