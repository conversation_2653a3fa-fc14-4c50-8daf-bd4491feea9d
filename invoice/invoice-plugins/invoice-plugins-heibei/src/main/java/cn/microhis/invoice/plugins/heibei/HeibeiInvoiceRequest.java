package cn.microhis.invoice.plugins.heibei;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 河北发票创建请求参数
 */
@Data
public class HeibeiInvoiceRequest {
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 机构代码
     */
    private String orgCode;
    
    /**
     * 发票类型：OUTPATIENT-门诊，INPATIENT-住院，REGISTRATION-挂号
     */
    private String invoiceType;
    
    /**
     * 供应商流水号
     */
    private String providerKey;
    
    /**
     * 销售方名称
     */
    private String sellerName;
    
    /**
     * 销售方纳税人识别号
     */
    private String sellerTaxNo;
    
    /**
     * 销售方电话
     */
    private String sellerPhone;
    
    /**
     * 销售方地址
     */
    private String sellerAddress;
    
    /**
     * 销售方开户行
     */
    private String sellerBank;
    
    /**
     * 销售方银行账号
     */
    private String sellerBankAccount;
    
    /**
     * 购买方姓名
     */
    private String buyerName;
    
    /**
     * 购买方电话
     */
    private String buyerPhone;
    
    /**
     * 购买方邮箱
     */
    private String buyerEmail;
    
    /**
     * 购买方身份证号
     */
    private String buyerIdCard;
    
    /**
     * 发票总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 个人自费金额
     */
    private BigDecimal ownPayAmount;
    
    /**
     * 医保卡号
     */
    private String medicalInsuranceNo;
    
    /**
     * 医保类型
     */
    private String medicalInsuranceType;
    
    /**
     * 医保统筹支付金额
     */
    private BigDecimal fundPayAmount;
    
    /**
     * 医保个账支付金额
     */
    private BigDecimal accountPayAmount;
    
    /**
     * 发票明细列表
     */
    private List<HeibeiInvoiceDetail> details;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 操作员
     */
    private String operator;
}
