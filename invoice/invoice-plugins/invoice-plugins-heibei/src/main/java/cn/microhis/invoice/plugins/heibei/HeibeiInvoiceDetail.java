package cn.microhis.invoice.plugins.heibei;

import cn.microhis.invoice.model.FeeType;
import cn.microhis.invoice.model.InvoiceDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 河北发票明细
 */
@Data
public class HeibeiInvoiceDetail {
    /**
     * 序号
     */
    private Integer serialNo;
    
    /**
     * 项目名称
     */
    private String itemName;
    
    /**
     * 项目代码
     */
    private String itemCode;
    
    /**
     * 规格型号
     */
    private String specification;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 数量
     */
    private BigDecimal quantity;
    
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 金额
     */
    private BigDecimal amount;
    
    /**
     * 税率
     */
    private BigDecimal taxRate;
    
    /**
     * 税额
     */
    private BigDecimal taxAmount;
    
    /**
     * 费用类别代码
     */
    private String feeTypeCode;
    
    /**
     * 费用类别名称
     */
    private String feeTypeName;
    
    /**
     * 供应商费用代码
     */
    private String providerFeeCode;
    
    /**
     * 从发票明细转换为河北发票明细
     */
    public static List<HeibeiInvoiceDetail> fromInvoiceDetails(List<InvoiceDetail> invoiceDetails, Map<String, FeeType> feeTypeMap) {
        if (invoiceDetails == null || invoiceDetails.isEmpty()) {
            return List.of();
        }
        
        return invoiceDetails.stream()
                .map(detail -> {
                    var heibeiDetail = new HeibeiInvoiceDetail();
                    heibeiDetail.setSerialNo(detail.getSerialNo());
                    heibeiDetail.setItemName(detail.getName());
                    heibeiDetail.setItemCode(detail.getCode());
                    heibeiDetail.setSpecification(detail.getSpecification());
                    heibeiDetail.setUnit(detail.getUnit());
                    heibeiDetail.setQuantity(detail.getQuantity());
                    heibeiDetail.setUnitPrice(detail.getUnitPrice());
                    heibeiDetail.setAmount(detail.getAmount());
                    heibeiDetail.setTaxRate(detail.getTaxRate() != null ? detail.getTaxRate() : BigDecimal.ZERO);
                    heibeiDetail.setTaxAmount(detail.getTaxAmount() != null ? detail.getTaxAmount() : BigDecimal.ZERO);
                    
                    // 设置费用类别信息
                    if (detail.getFeeTypeCode() != null) {
                        heibeiDetail.setFeeTypeCode(detail.getFeeTypeCode());
                        var feeType = feeTypeMap.get(detail.getFeeTypeCode());
                        if (feeType != null) {
                            heibeiDetail.setFeeTypeName(feeType.getName());
                            heibeiDetail.setProviderFeeCode(feeType.getProviderCode());
                        }
                    }
                    
                    return heibeiDetail;
                })
                .collect(Collectors.toList());
    }
}
