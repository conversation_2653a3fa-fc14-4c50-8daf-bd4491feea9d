package cn.microhis.invoice.plugins.heibei;

import cn.com.idmy.base.exception.BizException;
import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.provider.ProviderPlugin;
import cn.microhis.invoice.provider.ProviderPluginBase;
import cn.microhis.invoice.provider.exception.InvoiceConfigException;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.jetbrains.annotations.Nullable;

import static cn.com.idmy.base.util.Assert.notBlank;

/**
 * 河北发票供应商插件实现
 * 实现河北地区发票的创建、查询和冲正功能
 */
@Slf4j
public class HeibeiImpl extends ProviderPluginBase<HeibeiConfig> implements ProviderPlugin {
    
    public HeibeiImpl() {
        super("HEIBEI");
    }

    @Override
    protected HeibeiConfig parseConfig(long tenantId, JSONObject config) throws InvoiceConfigException {
        var cfg = new HeibeiConfig();
        
        // 必填配置项验证
        cfg.url = notBlank(config.getString("url"), "请配置服务器URL地址");
        cfg.appId = notBlank(config.getString("appId"), "请配置应用ID");
        cfg.appSecret = notBlank(config.getString("appSecret"), "请配置应用密钥");
        cfg.orgCode = notBlank(config.getString("orgCode"), "请配置机构代码");
        cfg.orgName = notBlank(config.getString("orgName"), "请配置机构名称");
        cfg.sellerTaxNo = notBlank(config.getString("sellerTaxNo"), "请配置销售方纳税人识别号");
        cfg.sellerName = notBlank(config.getString("sellerName"), "请配置销售方名称");
        
        // 可选配置项
        cfg.sellerPhone = config.getString("sellerPhone");
        cfg.sellerAddress = config.getString("sellerAddress");
        cfg.sellerBank = config.getString("sellerBank");
        cfg.sellerBankAccount = config.getString("sellerBankAccount");
        cfg.testMode = config.getBooleanValue("testMode", false);
        
        log.info("河北发票插件配置解析完成，租户ID: {}, 机构: {}", tenantId, cfg.orgName);
        return cfg;
    }

    @Override
    protected Invoice createOutpatient(Invoice invoice) {
        log.info("开始创建门诊发票，业务ID: {}", invoice.getBizId());
        
        var cfg = config();
        var mi = invoice.getMedicalInsurance();
        
        // 构建发票备注信息
        invoice.setRemark(StrUtil.format("科室：{}，医保类型：{}，医保统筹：{}，医保个账：{}，个人自费：{}",
                invoice.getDept(),
                mi == null || mi.getType() == null ? "无" : mi.getType().title(),
                fmtAmt(mi == null ? 0 : mi.getFundPay()),
                fmtAmt(mi == null ? 0 : mi.getAccountPay()),
                fmtAmt(invoice.getOwnPay())));
        
        // 调用河北发票服务创建门诊发票
        return createInvoiceByType(invoice, "OUTPATIENT");
    }

    @Override
    protected Invoice createInpatient(Invoice invoice) {
        log.info("开始创建住院发票，业务ID: {}", invoice.getBizId());
        
        var cfg = config();
        var inpatient = invoice.getInpatient();
        var mi = invoice.getMedicalInsurance();
        
        // 构建住院发票备注信息
        invoice.setRemark(StrUtil.format("入院科室：{}，出院科室：{}，入院日期：{}，出院日期：{}，医保类型：{}，医保统筹：{}，医保个账：{}，个人自费：{}",
                invoice.getDept(),
                inpatient.getOutDept(),
                DateUtil.format(inpatient.getInAt(), "yyyy-MM-dd"),
                DateUtil.format(inpatient.getOutAt(), "yyyy-MM-dd"),
                mi == null || mi.getType() == null ? "无" : mi.getType().title(),
                fmtAmt(mi == null ? 0 : mi.getFundPay()),
                fmtAmt(mi == null ? 0 : mi.getAccountPay()),
                fmtAmt(invoice.getOwnPay())));
        
        // 调用河北发票服务创建住院发票
        return createInvoiceByType(invoice, "INPATIENT");
    }

    @Override
    protected Invoice createRegistration(Invoice invoice) {
        log.info("开始创建挂号发票，业务ID: {}", invoice.getBizId());
        
        // 挂号发票按门诊发票处理
        return createOutpatient(invoice);
    }

    @Override
    protected @Nullable InvoiceReversal doReverse(Invoice invoice) {
        log.info("开始冲红发票，业务ID: {}", invoice.getBizId());
        
        var cfg = config();
        
        // 检查发票是否已开具
        if (StrUtil.isBlank(invoice.getTicketCode()) || StrUtil.isBlank(invoice.getTicketNo())) {
            log.warn("发票未开具，无法冲红，业务ID: {}", invoice.getBizId());
            return null;
        }
        
        try {
            // 构建冲红请求参数
            var reversalRequest = buildReversalRequest(invoice);
            
            // 调用河北发票服务进行冲红
            var result = callHeibeiReversalApi(cfg, reversalRequest);
            
            if (result != null && result.isSuccess()) {
                return InvoiceReversal.builder()
                        .ticketCode(result.getReversalTicketCode())
                        .ticketNo(result.getReversalTicketNo())
                        .ticketAt(result.getReversalTime())
                        .checkCode(result.getCheckCode())
                        .url(result.getReversalUrl())
                        .build();
            } else {
                log.error("河北发票冲红失败，业务ID: {}, 错误信息: {}", 
                    invoice.getBizId(), result != null ? result.getErrorMessage() : "未知错误");
                return null;
            }
        } catch (Exception e) {
            log.error("河北发票冲红异常，业务ID: {}", invoice.getBizId(), e);
            throw new BizException("发票冲红失败: " + e.getMessage());
        }
    }

    /**
     * 根据发票类型创建发票
     */
    private Invoice createInvoiceByType(Invoice invoice, String invoiceType) {
        var cfg = config();
        
        try {
            // 构建发票请求参数
            var invoiceRequest = buildInvoiceRequest(invoice, invoiceType);
            
            // 调用河北发票服务
            var result = callHeibeiInvoiceApi(cfg, invoiceRequest);
            
            if (result != null && result.isSuccess()) {
                // 更新发票信息
                invoice.setTicketCode(result.getTicketCode());
                invoice.setTicketNo(result.getTicketNo());
                invoice.setCheckCode(result.getCheckCode());
                invoice.setUrl(result.getInvoiceUrl());
                invoice.setTicketAt(result.getCreateTime());
                
                log.info("河北发票创建成功，业务ID: {}, 发票代码: {}, 发票号码: {}", 
                    invoice.getBizId(), result.getTicketCode(), result.getTicketNo());
                return invoice;
            } else {
                String errorMsg = result != null ? result.getErrorMessage() : "未知错误";
                log.error("河北发票创建失败，业务ID: {}, 错误信息: {}", invoice.getBizId(), errorMsg);
                throw new BizException("发票创建失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("河北发票创建异常，业务ID: {}", invoice.getBizId(), e);
            throw new BizException("发票创建失败: " + e.getMessage());
        }
    }

    /**
     * 构建发票请求参数
     */
    private HeibeiInvoiceRequest buildInvoiceRequest(Invoice invoice, String invoiceType) {
        var cfg = config();
        var request = new HeibeiInvoiceRequest();
        
        // 基本信息
        request.setAppId(cfg.appId);
        request.setOrgCode(cfg.orgCode);
        request.setInvoiceType(invoiceType);
        request.setProviderKey(invoice.getProviderKey());
        
        // 销售方信息
        request.setSellerName(cfg.sellerName);
        request.setSellerTaxNo(cfg.sellerTaxNo);
        request.setSellerPhone(cfg.sellerPhone);
        request.setSellerAddress(cfg.sellerAddress);
        request.setSellerBank(cfg.sellerBank);
        request.setSellerBankAccount(cfg.sellerBankAccount);
        
        // 购买方信息
        request.setBuyerName(invoice.getName());
        request.setBuyerPhone(invoice.getMobile());
        request.setBuyerEmail(invoice.getEmail());
        request.setBuyerIdCard(invoice.getCardNo());
        
        // 发票金额信息
        request.setTotalAmount(invoice.getAmt());
        request.setOwnPayAmount(invoice.getOwnPay());
        
        // 医保信息
        var mi = invoice.getMedicalInsurance();
        if (mi != null) {
            request.setMedicalInsuranceNo(mi.getNo());
            request.setMedicalInsuranceType(mi.getType() != null ? mi.getType().title() : null);
            request.setFundPayAmount(mi.getFundPay());
            request.setAccountPayAmount(mi.getAccountPay());
        }
        
        // 发票明细
        var feeTypeMap = mapFeeType();
        var details = HeibeiInvoiceDetail.fromInvoiceDetails(invoice.getDetails(), feeTypeMap);
        request.setDetails(details);
        
        // 备注信息
        request.setRemark(invoice.getRemark());
        request.setOperator(invoice.getPayee());
        
        return request;
    }

    /**
     * 构建冲红请求参数
     */
    private HeibeiReversalRequest buildReversalRequest(Invoice invoice) {
        var cfg = config();
        var request = new HeibeiReversalRequest();
        
        request.setAppId(cfg.appId);
        request.setOrgCode(cfg.orgCode);
        request.setOriginalTicketCode(invoice.getTicketCode());
        request.setOriginalTicketNo(invoice.getTicketNo());
        request.setReversalReason(invoice.getRemark());
        request.setOperator(invoice.getPayee());
        
        return request;
    }

    /**
     * 调用河北发票API创建发票
     */
    private HeibeiInvoiceResponse callHeibeiInvoiceApi(HeibeiConfig cfg, HeibeiInvoiceRequest request) {
        log.info("调用河北发票API创建发票，机构: {}", cfg.orgCode);

        try {
            // 调用河北发票服务创建发票
            return Http.post(cfg, request, "/api/invoice/create", HeibeiInvoiceResponse.class);
        } catch (Exception e) {
            log.error("调用河北发票API创建发票失败", e);

            // 返回失败响应
            var response = new HeibeiInvoiceResponse();
            response.setSuccess(false);
            response.setErrorMessage(e.getMessage());
            return response;
        }
    }

    /**
     * 调用河北发票API进行冲红
     */
    private HeibeiReversalResponse callHeibeiReversalApi(HeibeiConfig cfg, HeibeiReversalRequest request) {
        log.info("调用河北发票API进行冲红，机构: {}", cfg.orgCode);

        try {
            // 调用河北发票服务进行冲红
            return Http.post(cfg, request, "/api/invoice/reverse", HeibeiReversalResponse.class);
        } catch (Exception e) {
            log.error("调用河北发票API冲红失败", e);

            // 返回失败响应
            var response = new HeibeiReversalResponse();
            response.setSuccess(false);
            response.setErrorMessage(e.getMessage());
            return response;
        }
    }

    @Override
    public void scheduledTask() {
        log.info("河北发票定时任务开始执行");
        // TODO: 实现定时任务逻辑，如发票状态查询、同步等
    }
}
