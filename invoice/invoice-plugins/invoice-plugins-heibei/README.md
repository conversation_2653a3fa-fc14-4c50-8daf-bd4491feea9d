# 河北发票插件 (Heibei Invoice Plugin)

## 概述

河北发票插件是为河北地区医疗机构提供的电子发票服务插件，支持门诊、住院和挂号发票的创建、查询和冲红功能。

## 功能特性

- **多种发票类型支持**：门诊发票、住院发票、挂号发票
- **完整的发票生命周期管理**：创建、查询、冲红
- **医保信息集成**：支持医保统筹、个账支付信息
- **安全认证**：支持MD5签名验证
- **错误处理**：完善的异常处理和日志记录
- **配置灵活**：支持测试环境和生产环境配置

## 配置说明

### 必填配置项

| 配置项 | 说明 | 示例 |
|--------|------|------|
| url | 河北发票服务器地址 | https://api.heibei-invoice.com |
| appId | 应用ID | your_app_id |
| appSecret | 应用密钥 | your_app_secret |
| orgCode | 机构代码 | ORG001 |
| orgName | 机构名称 | 河北省某某医院 |
| sellerTaxNo | 销售方纳税人识别号 | 91130000000000000X |
| sellerName | 销售方名称 | 河北省某某医院 |

### 可选配置项

| 配置项 | 说明 | 示例 |
|--------|------|------|
| sellerPhone | 销售方电话 | 0311-******** |
| sellerAddress | 销售方地址 | 河北省石家庄市某某区某某路123号 |
| sellerBank | 销售方开户行 | 中国工商银行河北省分行 |
| sellerBankAccount | 销售方银行账号 | ********90********9 |
| testMode | 是否为测试环境 | false |

### 配置示例

```json
{
  "url": "https://api.heibei-invoice.com",
  "appId": "your_app_id_here",
  "appSecret": "your_app_secret_here",
  "orgCode": "your_organization_code",
  "orgName": "河北省某某医院",
  "sellerTaxNo": "91130000000000000X",
  "sellerName": "河北省某某医院",
  "sellerPhone": "0311-********",
  "sellerAddress": "河北省石家庄市某某区某某路123号",
  "sellerBank": "中国工商银行河北省分行",
  "sellerBankAccount": "********90********9",
  "testMode": false
}
```

## API接口

### 发票创建接口

- **路径**: `/api/invoice/create`
- **方法**: POST
- **功能**: 创建电子发票

### 发票冲红接口

- **路径**: `/api/invoice/reverse`
- **方法**: POST
- **功能**: 冲红已开具的发票

## 签名算法

插件使用MD5签名算法确保请求安全性：

1. 将请求参数按字典序排列
2. 拼接成 `key1=value1&key2=value2&key=appSecret` 格式
3. 对拼接字符串进行MD5加密
4. 将结果转换为大写十六进制字符串

## 错误处理

插件提供完善的错误处理机制：

- **配置错误**: 在插件初始化时检查必填配置项
- **网络错误**: 自动重试和超时处理
- **业务错误**: 根据河北发票服务返回的错误码进行相应处理
- **日志记录**: 详细的操作日志和错误日志

## 使用说明

1. **配置插件**: 在系统中配置河北发票插件的相关参数
2. **创建发票**: 调用发票创建接口，系统会自动选择河北发票插件
3. **查看发票**: 通过返回的发票URL查看电子发票
4. **冲红发票**: 如需冲红，调用发票冲红接口

## 注意事项

1. 确保网络连接正常，能够访问河北发票服务器
2. 妥善保管appId和appSecret，避免泄露
3. 测试环境和生产环境使用不同的配置
4. 定期检查发票服务的可用性
5. 遵循河北发票服务的调用频率限制

## 技术支持

如有问题，请联系技术支持团队或查看系统日志获取详细错误信息。
