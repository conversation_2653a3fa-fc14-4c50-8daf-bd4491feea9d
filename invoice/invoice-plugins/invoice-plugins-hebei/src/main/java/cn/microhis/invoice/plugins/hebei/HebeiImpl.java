package cn.microhis.invoice.plugins.hebei;

import cn.com.idmy.base.exception.BizException;
import cn.microhis.invoice.model.Invoice;
import cn.microhis.invoice.model.InvoiceReversal;
import cn.microhis.invoice.provider.ProviderPlugin;
import cn.microhis.invoice.provider.ProviderPluginBase;
import cn.microhis.invoice.provider.exception.InvoiceConfigException;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static cn.com.idmy.base.util.Assert.notBlank;

/**
 * 河北发票供应商插件实现
 * 实现河北地区发票的创建、查询和冲正功能
 * 根据河北省财政非税电子票据系统单位接口规范（医疗）
 */
@Slf4j
public class HebeiImpl extends ProviderPluginBase<HebeiConfig> implements ProviderPlugin {

    public HebeiImpl() {
        super("HEBEI");
    }

    @Override
    protected HebeiConfig parseConfig(long tenantId, JSONObject config) throws InvoiceConfigException {
        var cfg = new HebeiConfig();

        // 必填配置项验证
        cfg.url = notBlank(config.getString("url"), "请配置服务器URL地址");
        cfg.appId = notBlank(config.getString("appId"), "请配置应用ID");
        cfg.appSecret = notBlank(config.getString("appSecret"), "请配置应用密钥");
        cfg.orgCode = notBlank(config.getString("orgCode"), "请配置机构代码");
        cfg.orgName = notBlank(config.getString("orgName"), "请配置机构名称");
        cfg.sellerTaxNo = notBlank(config.getString("sellerTaxNo"), "请配置销售方纳税人识别号");
        cfg.sellerName = notBlank(config.getString("sellerName"), "请配置销售方名称");

        // 可选配置项
        cfg.sellerPhone = config.getString("sellerPhone");
        cfg.sellerAddress = config.getString("sellerAddress");
        cfg.sellerBank = config.getString("sellerBank");
        cfg.sellerBankAccount = config.getString("sellerBankAccount");
        cfg.testMode = config.getBooleanValue("testMode", false);

        log.info("河北发票插件配置解析完成，租户ID: {}, 机构: {}", tenantId, cfg.orgName);
        return cfg;
    }

    @Override
    protected Invoice createOutpatient(Invoice invoice) {
        log.info("开始创建门诊发票，业务ID: {}", invoice.getBizId());

        try {
            // 构建河北政府门诊发票请求参数
            var request = buildOutpatientRequest(invoice);

            // 调用河北政府发票服务
            var result = callHebeiInvoiceApi(request);

            if (result != null && result.isSuccess()) {
                // 更新发票信息
                invoice.setTicketCode(result.getTicketCode());
                invoice.setTicketNo(result.getTicketNo());
                invoice.setCheckCode(result.getCheckCode());
                invoice.setUrl(result.getInvoiceUrl());
                invoice.setTicketAt(result.getCreateTime());

                log.info("河北门诊发票创建成功，业务ID: {}, 发票代码: {}, 发票号码: {}",
                    invoice.getBizId(), result.getTicketCode(), result.getTicketNo());
                return invoice;
            } else {
                String errorMsg = result != null ? result.getErrorMessage() : "未知错误";
                log.error("河北门诊发票创建失败，业务ID: {}, 错误信息: {}", invoice.getBizId(), errorMsg);
                throw new BizException("门诊发票创建失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("河北门诊发票创建异常，业务ID: {}", invoice.getBizId(), e);
            throw new BizException("门诊发票创建失败: " + e.getMessage());
        }
    }

    @Override
    protected Invoice createInpatient(Invoice invoice) {
        log.info("开始创建住院发票，业务ID: {}", invoice.getBizId());

        try {
            // 构建河北政府住院发票请求参数
            var request = buildInpatientRequest(invoice);

            // 调用河北政府发票服务
            var result = callHebeiInvoiceApi(request);

            if (result != null && result.isSuccess()) {
                // 更新发票信息
                invoice.setTicketCode(result.getTicketCode());
                invoice.setTicketNo(result.getTicketNo());
                invoice.setCheckCode(result.getCheckCode());
                invoice.setUrl(result.getInvoiceUrl());
                invoice.setTicketAt(result.getCreateTime());

                log.info("河北住院发票创建成功，业务ID: {}, 发票代码: {}, 发票号码: {}",
                    invoice.getBizId(), result.getTicketCode(), result.getTicketNo());
                return invoice;
            } else {
                String errorMsg = result != null ? result.getErrorMessage() : "未知错误";
                log.error("河北住院发票创建失败，业务ID: {}, 错误信息: {}", invoice.getBizId(), errorMsg);
                throw new BizException("住院发票创建失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("河北住院发票创建异常，业务ID: {}", invoice.getBizId(), e);
            throw new BizException("住院发票创建失败: " + e.getMessage());
        }
    }

    @Override
    protected Invoice createRegistration(Invoice invoice) {
        log.info("开始创建挂号发票，业务ID: {}", invoice.getBizId());

        // 挂号发票按门诊发票处理
        return createOutpatient(invoice);
    }

    @Override
    protected @Nullable InvoiceReversal doReverse(Invoice invoice) {
        log.info("开始冲红发票，业务ID: {}", invoice.getBizId());

        // 检查发票是否已开具
        if (StrUtil.isBlank(invoice.getTicketCode()) || StrUtil.isBlank(invoice.getTicketNo())) {
            log.warn("发票未开具，无法冲红，业务ID: {}", invoice.getBizId());
            return null;
        }

        try {
            // 构建冲红请求参数
            var request = buildReversalRequest(invoice);

            // 调用河北发票服务进行冲红
            var result = callHebeiReversalApi(request);

            if (result != null && result.isSuccess()) {
                return InvoiceReversal.builder()
                        .ticketCode(result.getReversalTicketCode())
                        .ticketNo(result.getReversalTicketNo())
                        .ticketAt(result.getReversalTime())
                        .checkCode(result.getCheckCode())
                        .url(result.getReversalUrl())
                        .build();
            } else {
                log.error("河北发票冲红失败，业务ID: {}, 错误信息: {}",
                    invoice.getBizId(), result != null ? result.getErrorMessage() : "未知错误");
                return null;
            }
        } catch (Exception e) {
            log.error("河北发票冲红异常，业务ID: {}", invoice.getBizId(), e);
            throw new BizException("发票冲红失败: " + e.getMessage());
        }
    }
