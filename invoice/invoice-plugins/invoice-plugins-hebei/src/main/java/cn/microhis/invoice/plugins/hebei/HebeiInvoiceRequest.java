package cn.microhis.invoice.plugins.hebei;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 河北政府发票创建请求参数
 * 根据河北省财政非税电子票据系统单位接口规范（医疗）
 */
@Data
public class HebeiInvoiceRequest {
    
    // ========== 基本信息 ==========
    
    /**
     * 电子票据模板 1:门诊 2:住院
     */
    private String einvoicespecimen;
    
    /**
     * 开票日期 yyyyMMdd
     */
    private String issuedate;
    
    /**
     * 开票时间 hh:mm:ss
     */
    private String issuetime;
    
    /**
     * 交款人类型 1:个人 2:单位
     */
    private String payerpartytype;
    
    /**
     * 交款人代码 (身份证号等)
     */
    private String payerpartycode;
    
    /**
     * 交款人名称
     */
    private String payerpartyname;
    
    /**
     * 交款人账号
     */
    private String payeracct;
    
    /**
     * 交款人开户行
     */
    private String payeropbk;
    
    /**
     * 交款人手机号
     */
    private String payerphonenumber;
    
    /**
     * 收款人
     */
    private String recname;
    
    /**
     * 收款人账号
     */
    private String recacct;
    
    /**
     * 收款人开户行
     */
    private String recopbk;
    
    /**
     * 总金额
     */
    private BigDecimal totalamount;
    
    /**
     * 业务流水号 (唯一)
     */
    private String bizcode;
    
    /**
     * 开票人（收款人）
     */
    private String handlingperson;
    
    /**
     * 复核人
     */
    private String checker;
    
    /**
     * 其他信息（备注）
     */
    private String remark;
    
    /**
     * 相关票据代码 (红票时填写原票据代码)
     */
    private String relatedinvoicecode;
    
    /**
     * 相关票据号码 (红票时填写原票据号码)
     */
    private String relatedinvoicenumber;
    
    // ========== 医疗业务信息 ==========
    
    /**
     * 业务单号
     */
    private String businessnumber;
    
    /**
     * 业务日期 yyyymmdd
     */
    private String businessdate;
    
    /**
     * 性别 男、女
     */
    private String gender;
    
    /**
     * 医疗类别 门诊、急诊、住院等
     */
    private String medicaltype;
    
    /**
     * 门诊号
     */
    private String patientnumber;
    
    /**
     * 就诊日期 yyyymmdd
     */
    private String medicaldate;
    
    /**
     * 医疗机构类型
     */
    private String orgtype;
    
    /**
     * 医保类型
     */
    private String medicalinsurancetype;
    
    /**
     * 医保编号
     */
    private String medicalinsuranceid;
    
    /**
     * 医保统筹基金支付
     */
    private BigDecimal fundpayamount;
    
    /**
     * 其他支付
     */
    private BigDecimal otherpayamount;
    
    /**
     * 个人账户支付
     */
    private BigDecimal accountpayamount;
    
    /**
     * 个人现金支付
     */
    private BigDecimal ownpayamount;
    
    /**
     * 个人自付
     */
    private BigDecimal selfpaymentamount;
    
    /**
     * 个人自费
     */
    private BigDecimal selfpaymentcost;
    
    /**
     * 获取返回参数方式 1:直接返回data 2:无data，后续发主动获取接口查询
     */
    private Integer state;
    
    /**
     * 项目信息列表
     */
    private List<HebeiInvoiceDetail> details;
    
    /**
     * 项目清单列表
     */
    private List<HebeiInvoiceAuxDetail> auxdetails;
}
