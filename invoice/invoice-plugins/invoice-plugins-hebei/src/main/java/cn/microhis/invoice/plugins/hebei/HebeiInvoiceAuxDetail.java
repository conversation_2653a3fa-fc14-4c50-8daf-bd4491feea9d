package cn.microhis.invoice.plugins.hebei;

import cn.microhis.invoice.model.InvoiceDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 河北政府发票辅助明细 - 项目清单
 * 根据河北省财政非税电子票据系统单位接口规范（医疗）
 */
@Data
public class HebeiInvoiceAuxDetail {
    
    /**
     * 对应项目编码 - 收费明细项目对应的项目编码
     */
    private String auxitemrelatedcode;
    
    /**
     * 对应项目名称 - 收费明细项目对应的项目名称
     */
    private String auxitemrelatedname;
    
    /**
     * 收费明细项目编码
     */
    private String auxitemcode;
    
    /**
     * 收费明细项目名称
     */
    private String auxitemname;
    
    /**
     * 收费明细项目数量
     */
    private BigDecimal auxitemquantity;
    
    /**
     * 收费明细项目单位
     */
    private String auxitemunit;
    
    /**
     * 收费明细项目标准
     */
    private BigDecimal auxitemstd;
    
    /**
     * 收费明细项目金额
     */
    private BigDecimal auxitemamount;
    
    /**
     * 收费明细项目备注
     */
    private String auxitemremark;
    
    /**
     * 从发票明细转换为河北政府发票辅助明细
     */
    public static List<HebeiInvoiceAuxDetail> fromInvoiceDetails(List<InvoiceDetail> invoiceDetails) {
        if (invoiceDetails == null || invoiceDetails.isEmpty()) {
            return List.of();
        }
        
        return invoiceDetails.stream()
                .map(detail -> {
                    var auxDetail = new HebeiInvoiceAuxDetail();
                    auxDetail.setAuxitemrelatedcode(detail.getFeeTypeCode());
                    auxDetail.setAuxitemrelatedname(detail.getFeeTypeName());
                    auxDetail.setAuxitemcode(detail.getCode());
                    auxDetail.setAuxitemname(detail.getName());
                    auxDetail.setAuxitemquantity(detail.getQuantity());
                    auxDetail.setAuxitemunit(detail.getUnit());
                    auxDetail.setAuxitemstd(detail.getUnitPrice());
                    auxDetail.setAuxitemamount(detail.getAmount());
                    auxDetail.setAuxitemremark(detail.getRemark());
                    
                    return auxDetail;
                })
                .collect(Collectors.toList());
    }
}
