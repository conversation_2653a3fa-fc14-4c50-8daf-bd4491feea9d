package cn.microhis.invoice.plugins.hebei;

import cn.microhis.invoice.model.FeeType;
import cn.microhis.invoice.model.InvoiceDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 河北政府发票明细 - 项目信息
 * 根据河北省财政非税电子票据系统单位接口规范（医疗）
 */
@Data
public class HebeiInvoiceDetail {
    
    /**
     * 项目编码
     */
    private String itemcode;
    
    /**
     * 项目名称
     */
    private String itemname;
    
    /**
     * 数量
     */
    private BigDecimal itemquantity;
    
    /**
     * 单位
     */
    private String itemunit;
    
    /**
     * 标准
     */
    private BigDecimal itemstd;
    
    /**
     * 金额
     */
    private BigDecimal itemamount;
    
    /**
     * 项目备注
     */
    private String itemremark;
    
    /**
     * 从发票明细转换为河北政府发票明细
     */
    public static List<HebeiInvoiceDetail> fromInvoiceDetails(List<InvoiceDetail> invoiceDetails, Map<String, FeeType> feeTypeMap) {
        if (invoiceDetails == null || invoiceDetails.isEmpty()) {
            return List.of();
        }
        
        return invoiceDetails.stream()
                .map(detail -> {
                    var hebeiDetail = new HebeiInvoiceDetail();
                    hebeiDetail.setItemcode(detail.getCode());
                    hebeiDetail.setItemname(detail.getName());
                    hebeiDetail.setItemquantity(detail.getQuantity());
                    hebeiDetail.setItemunit(detail.getUnit());
                    hebeiDetail.setItemstd(detail.getUnitPrice());
                    hebeiDetail.setItemamount(detail.getAmount());
                    hebeiDetail.setItemremark(detail.getRemark());
                    
                    return hebeiDetail;
                })
                .collect(Collectors.toList());
    }
}
