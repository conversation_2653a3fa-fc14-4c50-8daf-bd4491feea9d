package cn.microhis.invoice.model;


import cn.com.idmy.base.annotation.Id;
import cn.com.idmy.base.annotation.IdType;
import cn.com.idmy.base.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Table(schema = "mh_invoice", name = "convert", title = "转换表")
public class Convert {
    @Id(type = IdType.AUTO)
    private Long id;
    private Integer providerId;
    private String type;
    private String inCode;
    private String inName;
    private String outCode;
    private String outName;
    private String remark;
}